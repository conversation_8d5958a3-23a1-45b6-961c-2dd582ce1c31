from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from config.settings import settings
from routes import auth, pdf, chat

app = FastAPI(title="Learning App API", version="1.0.0")

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include routers
app.include_router(auth.router)
app.include_router(pdf.router)
app.include_router(chat.router)

# Health check endpoint
@app.get("/")
async def root():
    return {"message": "Learning App API is running"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host=settings.HOST, port=settings.PORT)
